# 动态库存管理系统演示

一个基于 Vue 3 + Pinia + TypeScript 的动态库存管理系统演示项目，展示了现代前端技术在企业级应用中的实际运用。

## ✨ 核心特性

- 🎨 **动态表单设计器** - 可视化设计产品档案表单，支持7种字段类型
- 📊 **智能库存管理** - 灵活设置库存字段，支持出入库操作和库存验证
- 📋 **完整操作日志** - 自动记录所有库存变动，提供可追溯的审计记录
- 📱 **二维码集成** - 每个产品自动生成唯一二维码，支持移动端管理
- 🔄 **实时数据同步** - 基于 Pinia 的响应式状态管理
- 💻 **现代化界面** - 基于 Element Plus 的美观用户界面

## 🚀 快速开始

### 环境要求

- Node.js 18+
- Bun (推荐) 或 npm/yarn

### 安装依赖

```bash
# 使用 Bun (推荐)
bun install

# 或使用 npm
npm install
```

### 启动开发服务器

```bash
# 使用 Bun
bun run dev

# 或使用 npm
npm run dev
```

### 构建生产版本

```bash
# 使用 Bun
bun run build

# 或使用 npm
npm run build
```

### 类型检查

```bash
bun run type-check
```

## 📖 演示指南

详细的演示步骤请参考 [DEMO_GUIDE.md](./DEMO_GUIDE.md)

## 🏗️ 技术架构

### 前端技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Pinia** - Vue 3 官方状态管理库
- **Element Plus** - 基于 Vue 3 的组件库
- **Vue Router** - Vue.js 官方路由管理器

### 构建工具

- **Vite** - 下一代前端构建工具
- **Rolldown** - 高性能 JavaScript 打包器

### 工具库

- **qrcode** - 二维码生成库
- **dayjs** - 轻量级日期处理库
- **uuid** - 唯一标识符生成库

## Project Setup

```sh
bun install
```

### Compile and Hot-Reload for Development

```sh
bun dev
```

### Type-Check, Compile and Minify for Production

```sh
bun run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
bun lint
```
