# 动态库存管理系统演示指南

## 系统概述

这是一个基于 Vue 3 + Pinia + TypeScript 的动态库存管理系统演示，具有以下核心特性：

- **动态表单设计器**：自定义产品档案表单
- **智能库存管理**：设置库存字段，进行出入库操作
- **完整操作日志**：所有库存变动的可追溯记录
- **二维码支持**：每个产品都有唯一的二维码身份

## 演示流程

### 第一步：设计产品表单 📝

1. 打开浏览器访问 `http://localhost:5174/`
2. 系统默认进入"表单设计器"页面
3. 从左侧"字段控件箱"添加字段：
   - 点击"单行文本"添加"产品名称"字段
   - 点击"单行文本"添加"产品规格"字段  
   - 点击"数字"添加"库存数量"字段
   - 点击"日期"添加"生产日期"字段

4. 配置字段：
   - 点击中间画布的"产品名称"字段
   - 在右侧配置面板修改标签为"产品名称"
   - 设置为"必填"
   - 设置提示文字为"请输入产品名称"

5. **关键步骤**：设置库存字段
   - 点击"库存数量"字段
   - 点击"设为库存字段"按钮
   - 系统会显示绿色的"库存"标识

### 第二步：录入产品数据 📊

1. 点击顶部导航切换到"数据管理中心"
2. 点击"新增产品"按钮
3. 在弹出的表单中录入：
   - 产品名称：华为 Mate 60 Pro
   - 产品规格：12GB+512GB 昆仑青
   - 库存数量：0（初始库存）
   - 生产日期：选择今天的日期

4. 点击"保存"完成第一个产品录入
5. 重复步骤录入更多产品：
   - iPhone 15 Pro Max 256GB 钛原色
   - 小米14 Ultra 16GB+1TB 黑色

### 第三步：库存操作 📦

1. 在数据表格中找到"华为 Mate 60 Pro"
2. 在库存列中：
   - 输入数量：100
   - 点击"入库"按钮
   - 观察库存数量变为100

3. 模拟销售出库：
   - 输入数量：30
   - 点击"出库"按钮
   - 观察库存数量变为70

4. 对其他产品进行类似操作

### 第四步：查看二维码 📱

1. 点击任意产品行的"二维码"按钮
2. 查看产品的唯一二维码
3. 可以下载二维码图片
4. 可以复制产品ID

### 第五步：查看操作日志 📋

1. 点击顶部导航切换到"出入库流水日志"
2. 查看统计信息：
   - 总操作次数
   - 总入库数量
   - 总出库数量
   - 净库存变化

3. 查看详细日志记录：
   - 操作时间
   - 产品名称
   - 操作类型（入库/出库）
   - 变动数量
   - 操作前后库存

4. 使用筛选功能：
   - 按操作类型筛选
   - 按日期范围筛选

## 系统特色功能

### 🎨 动态表单设计
- 支持7种字段类型：单行文本、多行文本、数字、日期、下拉选择、复选框、单选框
- 实时预览表单效果
- 灵活的字段配置选项
- 只能设置一个库存字段，确保数据一致性

### 📊 智能数据管理
- 动态生成数据表格
- 行内库存操作器，快速出入库
- 数据验证和错误处理
- 支持编辑和删除记录

### 🔍 完整操作追溯
- 自动记录所有库存变动
- 详细的操作前后状态
- 多维度数据统计
- 灵活的筛选和查询

### 📱 二维码集成
- 每个产品自动生成唯一二维码
- 支持下载二维码图片
- 便于移动端扫码管理

## 技术架构

- **前端框架**：Vue 3 + TypeScript
- **状态管理**：Pinia
- **UI组件库**：Element Plus
- **构建工具**：Vite + Rolldown
- **二维码生成**：qrcode.js
- **工具库**：dayjs, uuid

## 开发规范

- 响应式设计，支持不同屏幕尺寸
- TypeScript 严格类型检查
- 组件化开发，高度可复用
- 清晰的代码结构和注释
- 完善的错误处理和用户提示

## 扩展可能

这个演示系统可以进一步扩展为：
- 多用户权限管理
- 数据导入导出
- 库存预警功能
- 移动端应用
- 与ERP系统集成
- 条码扫描功能
- 库存盘点功能

---

**演示完成后，您可以按 Ctrl+C 停止开发服务器**
