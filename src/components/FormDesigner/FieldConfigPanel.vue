<template>
  <div class="field-config-panel">
    <div class="panel-header">
      <h3>字段配置</h3>
    </div>
    
    <div v-if="!selectedField" class="no-selection">
      <el-icon :size="48" color="#ccc">
        <Setting />
      </el-icon>
      <p>请在中间画布选择一个字段进行配置</p>
    </div>
    
    <div v-else class="config-content">
      <el-form :model="fieldConfig" label-width="80px" size="small">
        <!-- 基础配置 -->
        <div class="config-section">
          <h4>基础设置</h4>
          
          <el-form-item label="字段标签">
            <el-input 
              v-model="fieldConfig.label"
              placeholder="请输入字段标签"
              @input="updateLabel"
            />
          </el-form-item>
          
          <el-form-item label="字段类型">
            <el-tag>{{ getFieldTypeName(selectedField.type) }}</el-tag>
          </el-form-item>
          
          <el-form-item label="是否必填">
            <el-switch 
              v-model="fieldConfig.isRequired"
              @change="updateConfig"
            />
          </el-form-item>
          
          <el-form-item label="提示文字">
            <el-input 
              v-model="fieldConfig.placeholder"
              placeholder="请输入提示文字"
              @input="updateConfig"
            />
          </el-form-item>
        </div>
        
        <!-- 数字字段特殊配置 -->
        <div v-if="selectedField.type === FieldType.NUMBER_INPUT" class="config-section">
          <h4>数字设置</h4>
          
          <el-form-item label="最小值">
            <el-input-number 
              v-model="fieldConfig.min"
              :precision="0"
              @change="updateConfig"
            />
          </el-form-item>
          
          <el-form-item label="最大值">
            <el-input-number 
              v-model="fieldConfig.max"
              :precision="0"
              @change="updateConfig"
            />
          </el-form-item>
          
          <el-form-item label="库存字段">
            <el-switch 
              v-model="fieldConfig.isStockField"
              @change="updateStockField"
            />
            <div class="field-tip">
              设为库存字段后，系统将围绕此字段进行出入库操作
            </div>
          </el-form-item>
        </div>
        
        <!-- 选择类字段配置 -->
        <div v-if="isSelectField" class="config-section">
          <h4>选项设置</h4>
          
          <el-form-item label="选项列表">
            <div class="options-config">
              <div 
                v-for="(option, index) in fieldConfig.options" 
                :key="index"
                class="option-item"
              >
                <el-input 
                  v-model="fieldConfig.options[index]"
                  placeholder="请输入选项"
                  @input="updateConfig"
                />
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeOption(index)"
                >
                  删除
                </el-button>
              </div>
              
              <el-button 
                type="primary" 
                size="small" 
                @click="addOption"
              >
                添加选项
              </el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  ElForm, 
  ElFormItem, 
  ElInput, 
  ElInputNumber,
  ElSwitch, 
  ElTag, 
  ElButton,
  ElIcon 
} from 'element-plus'
import { Setting } from '@element-plus/icons-vue'
import { FieldType } from '@/types'
import { useFormBuilderStore } from '@/stores'

const formBuilderStore = useFormBuilderStore()

// 从父组件接收选中的字段ID
const props = defineProps<{
  selectedFieldId?: string
}>()

// 当前选中的字段
const selectedField = computed(() => {
  if (!props.selectedFieldId) return null
  return formBuilderStore.fields.find(f => f.id === props.selectedFieldId)
})

// 字段配置的本地副本
const fieldConfig = ref({
  label: '',
  isRequired: false,
  isStockField: false,
  placeholder: '',
  min: undefined as number | undefined,
  max: undefined as number | undefined,
  options: [] as string[]
})

// 是否为选择类字段
const isSelectField = computed(() => {
  if (!selectedField.value) return false
  return [FieldType.SELECT, FieldType.CHECKBOX, FieldType.RADIO].includes(selectedField.value.type)
})

// 监听选中字段变化，更新配置
watch(selectedField, (newField) => {
  if (newField) {
    fieldConfig.value = {
      label: newField.label,
      isRequired: newField.config.isRequired || false,
      isStockField: newField.config.isStockField || false,
      placeholder: newField.config.placeholder || '',
      min: newField.config.min,
      max: newField.config.max,
      options: newField.config.options ? [...newField.config.options] : ['选项1', '选项2', '选项3']
    }
  }
}, { immediate: true })

// 更新字段标签
const updateLabel = () => {
  if (selectedField.value) {
    formBuilderStore.updateFieldLabel(selectedField.value.id, fieldConfig.value.label)
  }
}

// 更新字段配置
const updateConfig = () => {
  if (selectedField.value) {
    const config = {
      isRequired: fieldConfig.value.isRequired,
      placeholder: fieldConfig.value.placeholder,
      min: fieldConfig.value.min,
      max: fieldConfig.value.max,
      options: fieldConfig.value.options.filter(opt => opt.trim() !== '')
    }
    formBuilderStore.updateFieldConfig(selectedField.value.id, config)
  }
}

// 更新库存字段设置
const updateStockField = () => {
  if (selectedField.value) {
    if (fieldConfig.value.isStockField) {
      formBuilderStore.setStockField(selectedField.value.id)
    } else {
      formBuilderStore.clearStockField()
    }
  }
}

// 添加选项
const addOption = () => {
  fieldConfig.value.options.push('新选项')
  updateConfig()
}

// 删除选项
const removeOption = (index: number) => {
  fieldConfig.value.options.splice(index, 1)
  updateConfig()
}

// 获取字段类型名称
const getFieldTypeName = (type: FieldType): string => {
  const typeNames = {
    [FieldType.SINGLE_LINE_TEXT]: '单行文本',
    [FieldType.MULTI_LINE_TEXT]: '多行文本',
    [FieldType.NUMBER_INPUT]: '数字',
    [FieldType.DATE_PICKER]: '日期',
    [FieldType.SELECT]: '下拉选择',
    [FieldType.CHECKBOX]: '复选框',
    [FieldType.RADIO]: '单选框'
  }
  return typeNames[type] || '未知类型'
}
</script>

<style scoped>
.field-config-panel {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  text-align: center;
}

.no-selection p {
  margin-top: 16px;
  font-size: 14px;
}

.config-content {
  flex: 1;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.config-section:last-child {
  border-bottom: none;
}

.config-section h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.field-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.options-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.option-item .el-input {
  flex: 1;
}
</style>
