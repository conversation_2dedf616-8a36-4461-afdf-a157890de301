<template>
  <div class="form-canvas">
    <div class="canvas-header">
      <h3>表单预览</h3>
      <div class="canvas-actions">
        <el-button v-if="fields.length > 0" type="danger" size="small" @click="clearAllFields">
          清空表单
        </el-button>
      </div>
    </div>

    <div class="canvas-content">
      <div v-if="fields.length === 0" class="empty-canvas">
        <el-icon :size="48" color="#ccc">
          <Document />
        </el-icon>
        <p>从左侧拖拽或点击字段控件开始设计表单</p>
      </div>

      <div v-else class="form-preview">
        <div
          v-for="field in fields"
          :key="field.id"
          class="field-item"
          :class="{ selected: selectedFieldId === field.id }"
          @click="selectField(field.id)"
        >
          <div class="field-header">
            <span class="field-label">
              {{ field.label }}
              <span v-if="field.config.isRequired" class="required">*</span>
              <span v-if="field.config.isStockField" class="stock-badge">库存</span>
            </span>
            <div class="field-actions">
              <el-button
                v-if="field.type === FieldType.NUMBER_INPUT && !field.config.isStockField"
                type="primary"
                size="small"
                @click.stop="setAsStockField(field.id)"
              >
                设为库存字段
              </el-button>
              <el-button
                v-if="field.config.isStockField"
                type="warning"
                size="small"
                @click.stop="clearStockField()"
              >
                取消库存字段
              </el-button>
              <el-button type="danger" size="small" @click.stop="removeField(field.id)">
                删除
              </el-button>
            </div>
          </div>

          <div class="field-preview">
            <FieldPreview :field="field" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { FieldType } from '@/types'
import { useFormBuilderStore } from '@/stores'
import FieldPreview from './FieldPreview.vue'

const formBuilderStore = useFormBuilderStore()
const { fields } = formBuilderStore

const selectedFieldId = ref<string>('')

const emit = defineEmits<{
  fieldSelected: [fieldId: string]
}>()

const selectField = (fieldId: string) => {
  selectedFieldId.value = fieldId
  emit('fieldSelected', fieldId)
}

const removeField = async (fieldId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个字段吗？', '确认删除', {
      type: 'warning',
    })
    formBuilderStore.removeField(fieldId)
    if (selectedFieldId.value === fieldId) {
      selectedFieldId.value = ''
    }
    ElMessage.success('字段已删除')
  } catch {
    // 用户取消删除
  }
}

const setAsStockField = (fieldId: string) => {
  formBuilderStore.setStockField(fieldId)
  ElMessage.success('已设置为库存字段')
}

const clearStockField = () => {
  formBuilderStore.clearStockField()
  ElMessage.success('已取消库存字段设置')
}

const clearAllFields = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有字段吗？此操作不可恢复。', '确认清空', {
      type: 'warning',
    })
    formBuilderStore.resetFields()
    selectedFieldId.value = ''
    ElMessage.success('表单已清空')
  } catch {
    // 用户取消清空
  }
}

// 暴露选中的字段ID给父组件
defineExpose({
  selectedFieldId,
})
</script>

<style scoped>
.form-canvas {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.canvas-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.canvas-content {
  flex: 1;
  overflow-y: auto;
}

.empty-canvas {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
  text-align: center;
}

.empty-canvas p {
  margin-top: 16px;
  font-size: 14px;
}

.form-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.field-item {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.field-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.field-item.selected {
  border-color: #409eff;
  background: #ecf5ff;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.field-label {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.required {
  color: #f56c6c;
}

.stock-badge {
  background: #67c23a;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.field-actions {
  display: flex;
  gap: 8px;
}

.field-preview {
  pointer-events: none;
}
</style>
