<template>
  <div class="field-palette">
    <div class="palette-header">
      <h3>字段控件箱</h3>
      <p>点击添加字段到表单</p>
    </div>

    <div class="field-types">
      <div
        v-for="fieldType in fieldTypes"
        :key="fieldType.type"
        class="field-type-item"
        @click="addField(fieldType.type)"
      >
        <div class="field-icon">
          <el-icon :size="20">
            <component :is="fieldType.icon" />
          </el-icon>
        </div>
        <div class="field-info">
          <div class="field-name">{{ fieldType.name }}</div>
          <div class="field-desc">{{ fieldType.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElIcon, ElMessage } from 'element-plus'
import {
  EditPen,
  Document,
  Operation,
  Calendar,
  ArrowDown,
  Check,
  CircleCheck,
} from '@element-plus/icons-vue'
import { FieldType } from '@/types'
import { useFormBuilderStore } from '@/stores'

const formBuilderStore = useFormBuilderStore()

// 字段类型配置
const fieldTypes = [
  {
    type: FieldType.SINGLE_LINE_TEXT,
    name: '单行文本',
    description: '输入简短文本信息',
    icon: EditPen,
  },
  {
    type: FieldType.MULTI_LINE_TEXT,
    name: '多行文本',
    description: '输入较长的文本内容',
    icon: Document,
  },
  {
    type: FieldType.NUMBER_INPUT,
    name: '数字',
    description: '输入数字，可设为库存字段',
    icon: Operation,
  },
  {
    type: FieldType.DATE_PICKER,
    name: '日期',
    description: '选择日期',
    icon: Calendar,
  },
  {
    type: FieldType.SELECT,
    name: '下拉选择',
    description: '从预设选项中选择',
    icon: ArrowDown,
  },
  {
    type: FieldType.CHECKBOX,
    name: '复选框',
    description: '多选选项',
    icon: Check,
  },
  {
    type: FieldType.RADIO,
    name: '单选框',
    description: '单选选项',
    icon: CircleCheck,
  },
]

const addField = (fieldType: FieldType) => {
  const fieldId = formBuilderStore.addField(fieldType)
  ElMessage.success(`已添加${fieldTypes.find((f) => f.type === fieldType)?.name}字段`)
}
</script>

<style scoped>
.field-palette {
  padding: 20px;
}

.palette-header {
  margin-bottom: 20px;
}

.palette-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.palette-header p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.field-types {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-type-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fafafa;
}

.field-type-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.field-icon {
  margin-right: 12px;
  color: #409eff;
}

.field-info {
  flex: 1;
}

.field-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.field-desc {
  font-size: 12px;
  color: #666;
}
</style>
