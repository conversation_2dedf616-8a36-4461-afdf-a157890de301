<template>
  <el-dialog
    v-model="visible"
    title="产品二维码"
    width="400px"
    @close="onClose"
  >
    <div class="qr-content">
      <div v-if="record" class="product-info">
        <h4>{{ getProductName(record) }}</h4>
        <p class="record-id">ID: {{ record.recordId }}</p>
      </div>
      
      <div class="qr-code-container">
        <canvas ref="qrCanvas" class="qr-canvas"></canvas>
      </div>
      
      <div class="qr-actions">
        <el-button @click="downloadQR">下载二维码</el-button>
        <el-button type="primary" @click="copyRecordId">复制ID</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElDialog, ElButton, ElMessage } from 'element-plus'
import QRCode from 'qrcode'
import { useFormBuilderStore, useFormDataStore } from '@/stores'
import type { ProductRecord } from '@/types'
import { FieldType } from '@/types'

interface Props {
  modelValue: boolean
  recordId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formBuilderStore = useFormBuilderStore()
const formDataStore = useFormDataStore()

const qrCanvas = ref<HTMLCanvasElement>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const record = computed(() => {
  return formDataStore.getRecord(props.recordId)
})

// 获取产品名称
const getProductName = (record: ProductRecord): string => {
  // 查找第一个单行文本字段作为产品名称
  const nameField = formBuilderStore.fields.find(f => 
    f.type === FieldType.SINGLE_LINE_TEXT
  )
  
  if (nameField && record.data[nameField.id]) {
    return String(record.data[nameField.id])
  }
  
  return '未命名产品'
}

// 生成二维码
const generateQRCode = async () => {
  if (!qrCanvas.value || !props.recordId) return
  
  try {
    await QRCode.toCanvas(qrCanvas.value, props.recordId, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  }
}

// 下载二维码
const downloadQR = () => {
  if (!qrCanvas.value) return
  
  const link = document.createElement('a')
  link.download = `qr-${props.recordId}.png`
  link.href = qrCanvas.value.toDataURL()
  link.click()
  
  ElMessage.success('二维码已下载')
}

// 复制记录ID
const copyRecordId = async () => {
  try {
    await navigator.clipboard.writeText(props.recordId)
    ElMessage.success('ID已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = props.recordId
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('ID已复制到剪贴板')
  }
}

// 监听弹窗显示状态
watch(visible, async (newVisible) => {
  if (newVisible && props.recordId) {
    await nextTick()
    generateQRCode()
  }
})

// 关闭弹窗
const onClose = () => {
  visible.value = false
}
</script>

<style scoped>
.qr-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.product-info {
  margin-bottom: 20px;
}

.product-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.record-id {
  margin: 0;
  color: #666;
  font-size: 12px;
  font-family: monospace;
}

.qr-code-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.qr-canvas {
  display: block;
}

.qr-actions {
  display: flex;
  gap: 12px;
}
</style>
