<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑产品' : '新增产品'"
    width="600px"
    @close="onClose"
  >
    <el-form 
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
    >
      <el-form-item 
        v-for="field in fields" 
        :key="field.id"
        :label="field.label"
        :prop="field.id"
      >
        <!-- 单行文本 -->
        <el-input 
          v-if="field.type === FieldType.SINGLE_LINE_TEXT"
          v-model="formData[field.id]"
          :placeholder="field.config.placeholder || '请输入' + field.label"
        />
        
        <!-- 多行文本 -->
        <el-input 
          v-else-if="field.type === FieldType.MULTI_LINE_TEXT"
          v-model="formData[field.id]"
          type="textarea"
          :placeholder="field.config.placeholder || '请输入' + field.label"
          :rows="3"
        />
        
        <!-- 数字输入 -->
        <el-input-number 
          v-else-if="field.type === FieldType.NUMBER_INPUT"
          v-model="formData[field.id]"
          :placeholder="field.config.placeholder || '请输入数字'"
          :min="field.config.min"
          :max="field.config.max"
          :precision="0"
          style="width: 100%"
        />
        
        <!-- 日期选择 -->
        <el-date-picker 
          v-else-if="field.type === FieldType.DATE_PICKER"
          v-model="formData[field.id]"
          type="date"
          :placeholder="field.config.placeholder || '选择日期'"
          style="width: 100%"
        />
        
        <!-- 下拉选择 -->
        <el-select 
          v-else-if="field.type === FieldType.SELECT"
          v-model="formData[field.id]"
          :placeholder="field.config.placeholder || '请选择'"
          style="width: 100%"
        >
          <el-option 
            v-for="option in field.config.options"
            :key="option"
            :label="option"
            :value="option"
          />
        </el-select>
        
        <!-- 复选框 -->
        <el-checkbox-group 
          v-else-if="field.type === FieldType.CHECKBOX"
          v-model="formData[field.id]"
        >
          <el-checkbox 
            v-for="option in field.config.options"
            :key="option"
            :label="option"
          >
            {{ option }}
          </el-checkbox>
        </el-checkbox-group>
        
        <!-- 单选框 -->
        <el-radio-group 
          v-else-if="field.type === FieldType.RADIO"
          v-model="formData[field.id]"
        >
          <el-radio 
            v-for="option in field.config.options"
            :key="option"
            :label="option"
          >
            {{ option }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { 
  ElDialog, 
  ElForm, 
  ElFormItem, 
  ElInput, 
  ElInputNumber,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElCheckboxGroup,
  ElCheckbox,
  ElRadioGroup,
  ElRadio,
  ElButton,
  ElMessage,
  type FormInstance,
  type FormRules
} from 'element-plus'
import { useFormBuilderStore, useFormDataStore } from '@/stores'
import type { ProductRecord, RecordData } from '@/types'
import { FieldType } from '@/types'

interface Props {
  modelValue: boolean
  editRecord?: ProductRecord | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formBuilderStore = useFormBuilderStore()
const formDataStore = useFormDataStore()

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const fields = computed(() => formBuilderStore.fields)
const isEdit = computed(() => !!props.editRecord)

// 表单数据
const formData = reactive<RecordData>({})

// 表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {}
  
  fields.value.forEach(field => {
    if (field.config.isRequired) {
      rules[field.id] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
  })
  
  return rules
})

// 监听编辑记录变化
watch(() => props.editRecord, (record) => {
  if (record) {
    // 编辑模式，填充现有数据
    Object.keys(formData).forEach(key => delete formData[key])
    Object.assign(formData, record.data)
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}, { immediate: true })

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => delete formData[key])
  
  // 设置默认值
  fields.value.forEach(field => {
    switch (field.type) {
      case FieldType.NUMBER_INPUT:
        formData[field.id] = field.config.isStockField ? 0 : undefined
        break
      case FieldType.CHECKBOX:
        formData[field.id] = []
        break
      default:
        formData[field.id] = undefined
    }
  })
}

// 提交表单
const onSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    if (isEdit.value && props.editRecord) {
      // 更新记录
      const success = formDataStore.updateRecord(props.editRecord.recordId, formData)
      if (success) {
        ElMessage.success('更新成功')
        emit('success')
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 新增记录
      const recordId = formDataStore.addRecord(formData)
      ElMessage.success('新增成功')
      emit('success')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const onClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
