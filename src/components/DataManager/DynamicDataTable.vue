<template>
  <div class="dynamic-data-table">
    <div class="table-header">
      <h3>产品数据表</h3>
      <div class="table-actions">
        <el-button type="primary" @click="showAddModal = true">
          新增产品
        </el-button>
        <el-button 
          v-if="records.length > 0" 
          type="danger" 
          @click="clearAllData"
        >
          清空数据
        </el-button>
      </div>
    </div>
    
    <div class="table-content">
      <el-table 
        :data="records" 
        stripe 
        border
        style="width: 100%"
        empty-text="暂无数据，点击新增产品开始录入"
      >
        <!-- 动态列 -->
        <el-table-column 
          v-for="field in fields" 
          :key="field.id"
          :prop="field.id"
          :label="field.label"
          :width="getColumnWidth(field)"
        >
          <template #default="{ row }">
            <div v-if="field.config.isStockField" class="stock-cell">
              <span class="stock-value">{{ row.data[field.id] || 0 }}</span>
              <StockOperator :record-id="row.recordId" />
            </div>
            <span v-else>{{ formatCellValue(row.data[field.id], field) }}</span>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                type="primary" 
                size="small" 
                @click="editRecord(row)"
              >
                编辑
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                @click="showQRCode(row)"
              >
                二维码
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="deleteRecord(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 数据录入/编辑弹窗 -->
    <DataFormModal 
      v-model="showAddModal"
      :edit-record="editingRecord"
      @success="onDataSuccess"
    />
    
    <!-- 二维码展示弹窗 -->
    <QRCodeModal 
      v-model="showQRModal"
      :record-id="qrRecordId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { useFormBuilderStore, useFormDataStore } from '@/stores'
import type { ProductRecord, FieldDefinition } from '@/types'
import { FieldType } from '@/types'
import { formatTimestamp } from '@/utils'
import StockOperator from './StockOperator.vue'
import DataFormModal from './DataFormModal.vue'
import QRCodeModal from './QRCodeModal.vue'

const formBuilderStore = useFormBuilderStore()
const formDataStore = useFormDataStore()

const showAddModal = ref(false)
const showQRModal = ref(false)
const editingRecord = ref<ProductRecord | null>(null)
const qrRecordId = ref('')

// 计算属性
const fields = computed(() => formBuilderStore.fields)
const records = computed(() => formDataStore.records)

// 获取列宽
const getColumnWidth = (field: FieldDefinition): number => {
  if (field.config.isStockField) return 200
  if (field.type === FieldType.MULTI_LINE_TEXT) return 200
  if (field.type === FieldType.DATE_PICKER) return 120
  return 150
}

// 格式化单元格值
const formatCellValue = (value: any, field: FieldDefinition): string => {
  if (value === null || value === undefined) return ''
  
  switch (field.type) {
    case FieldType.DATE_PICKER:
      return value ? formatTimestamp(value) : ''
    case FieldType.CHECKBOX:
      return Array.isArray(value) ? value.join(', ') : ''
    default:
      return String(value)
  }
}

// 编辑记录
const editRecord = (record: ProductRecord) => {
  editingRecord.value = record
  showAddModal.value = true
}

// 删除记录
const deleteRecord = async (record: ProductRecord) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
      type: 'warning'
    })
    
    const success = formDataStore.deleteRecord(record.recordId)
    if (success) {
      ElMessage.success('记录已删除')
    } else {
      ElMessage.error('删除失败')
    }
  } catch {
    // 用户取消删除
  }
}

// 显示二维码
const showQRCode = (record: ProductRecord) => {
  qrRecordId.value = record.recordId
  showQRModal.value = true
}

// 清空所有数据
const clearAllData = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有数据吗？此操作不可恢复。', '确认清空', {
      type: 'warning'
    })
    
    formDataStore.resetRecords()
    ElMessage.success('数据已清空')
  } catch {
    // 用户取消清空
  }
}

// 数据操作成功回调
const onDataSuccess = () => {
  showAddModal.value = false
  editingRecord.value = null
}
</script>

<style scoped>
.dynamic-data-table {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.table-content {
  flex: 1;
  overflow: auto;
}

.stock-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stock-value {
  font-weight: 500;
  color: #409eff;
  min-width: 40px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

:deep(.el-table .el-table__empty-block) {
  padding: 40px 0;
}
</style>
