<template>
  <div class="stock-operator">
    <el-input-number
      v-model="quantity"
      :min="1"
      :max="9999"
      :precision="0"
      size="small"
      style="width: 80px"
      placeholder="数量"
    />
    <el-button 
      type="success" 
      size="small" 
      @click="inbound"
      :loading="loading"
    >
      入库
    </el-button>
    <el-button 
      type="warning" 
      size="small" 
      @click="outbound"
      :loading="loading"
    >
      出库
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElInputNumber, ElButton, ElMessage } from 'element-plus'
import { useFormDataStore } from '@/stores'
import { StockOperationType } from '@/types'

interface Props {
  recordId: string
}

const props = defineProps<Props>()

const formDataStore = useFormDataStore()

const quantity = ref<number>(1)
const loading = ref(false)

// 入库操作
const inbound = async () => {
  if (!quantity.value || quantity.value <= 0) {
    ElMessage.warning('请输入有效的入库数量')
    return
  }
  
  loading.value = true
  try {
    const result = formDataStore.operateStock({
      recordId: props.recordId,
      type: StockOperationType.INBOUND,
      quantity: quantity.value
    })
    
    if (result.success) {
      ElMessage.success(`入库成功，当前库存：${result.stockAfter}`)
      quantity.value = 1 // 重置数量
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('入库操作失败')
  } finally {
    loading.value = false
  }
}

// 出库操作
const outbound = async () => {
  if (!quantity.value || quantity.value <= 0) {
    ElMessage.warning('请输入有效的出库数量')
    return
  }
  
  loading.value = true
  try {
    const result = formDataStore.operateStock({
      recordId: props.recordId,
      type: StockOperationType.OUTBOUND,
      quantity: quantity.value
    })
    
    if (result.success) {
      ElMessage.success(`出库成功，当前库存：${result.stockAfter}`)
      quantity.value = 1 // 重置数量
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('出库操作失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.stock-operator {
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-input-number .el-input__wrapper) {
  padding: 0 4px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}
</style>
