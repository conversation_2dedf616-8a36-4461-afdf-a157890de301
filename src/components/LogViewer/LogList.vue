<template>
  <div class="log-list">
    <div class="list-header">
      <h3>操作记录</h3>
      <div class="filter-controls">
        <el-select
          v-model="filterType"
          placeholder="筛选类型"
          style="width: 120px"
          @change="applyFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="入库" value="入库" />
          <el-option label="出库" value="出库" />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
          @change="applyFilter"
        />

        <el-button @click="resetFilter">重置筛选</el-button>
      </div>
    </div>

    <div class="list-content">
      <el-table
        :data="filteredLogs"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'timestamp', order: 'descending' }"
        empty-text="暂无符合条件的记录"
      >
        <el-table-column prop="timestamp" label="操作时间" width="160" sortable>
          <template #default="{ row }">
            {{ formatTimestamp(row.timestamp) }}
          </template>
        </el-table-column>

        <el-table-column prop="productName" label="产品名称" width="150" show-overflow-tooltip />

        <el-table-column prop="type" label="操作类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.type === '入库' ? 'success' : 'warning'" size="small">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="quantity" label="变动数量" width="100" align="center">
          <template #default="{ row }">
            <span :class="row.type === '入库' ? 'quantity-positive' : 'quantity-negative'">
              {{ row.type === '入库' ? '+' : '-' }}{{ row.quantity }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="stockBefore" label="操作前库存" width="120" align="center" />

        <el-table-column prop="stockAfter" label="操作后库存" width="120" align="center">
          <template #default="{ row }">
            <span class="stock-after">{{ row.stockAfter }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="recordId" label="记录ID" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="record-id">{{ row.recordId.slice(0, 8) }}...</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewRecord(row.recordId)">
              查看产品
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElMessage,
} from 'element-plus'
import { useLogStore, useFormDataStore } from '@/stores'
import { formatTimestamp } from '@/utils'
import type { OperationLog } from '@/types'

const logStore = useLogStore()
const formDataStore = useFormDataStore()

// 筛选状态
const filterType = ref<string>('')
const dateRange = ref<[Date, Date] | undefined>(undefined)

// 计算属性
const logs = computed(() => logStore.logs)

const filteredLogs = computed(() => {
  let result = logs.value

  // 按类型筛选
  if (filterType.value) {
    result = result.filter((log) => log.type === filterType.value)
  }

  // 按日期范围筛选
  if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    result = result.filter((log) => {
      const logDate = new Date(log.timestamp)
      return logDate >= startDate && logDate <= endDate
    })
  }

  return result
})

// 应用筛选
const applyFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

// 重置筛选
const resetFilter = () => {
  filterType.value = ''
  dateRange.value = undefined
}

// 查看产品记录
const viewRecord = (recordId: string) => {
  const record = formDataStore.getRecord(recordId)
  if (record) {
    // 这里可以跳转到数据管理页面并高亮显示对应记录
    // 或者显示产品详情弹窗
    ElMessage.info(`产品记录ID: ${recordId}`)
  } else {
    ElMessage.warning('产品记录不存在，可能已被删除')
  }
}
</script>

<style scoped>
.log-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.list-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.list-content {
  flex: 1;
  overflow: auto;
  padding: 0 20px 20px 20px;
}

.quantity-positive {
  color: #67c23a;
  font-weight: 500;
}

.quantity-negative {
  color: #f56c6c;
  font-weight: 500;
}

.stock-after {
  font-weight: 500;
  color: #409eff;
}

.record-id {
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

:deep(.el-table .el-table__row.inbound-row) {
  background-color: #f0f9ff;
}

:deep(.el-table .el-table__row.outbound-row) {
  background-color: #fef0f0;
}

:deep(.el-table .el-table__empty-block) {
  padding: 40px 0;
}
</style>
