import { useFormBuilderStore, useFormDataStore } from '@/stores'
import { FieldType, StockOperationType } from '@/types'
import type { FieldDefinition } from '@/types'

/**
 * 初始化演示数据
 */
export const initDemoData = () => {
  const formBuilderStore = useFormBuilderStore()
  const formDataStore = useFormDataStore()

  // 清空现有数据
  formBuilderStore.resetFields()
  formDataStore.resetRecords()

  // 创建演示表单字段
  const demoFields: Omit<FieldDefinition, 'id'>[] = [
    {
      type: FieldType.SINGLE_LINE_TEXT,
      label: '产品名称',
      config: {
        placeholder: '请输入产品名称',
        isRequired: true,
        isStockField: false,
      },
    },
    {
      type: FieldType.SINGLE_LINE_TEXT,
      label: '产品规格',
      config: {
        placeholder: '请输入产品规格',
        isRequired: false,
        isStockField: false,
      },
    },
    {
      type: FieldType.NUMBER_INPUT,
      label: '库存数量',
      config: {
        placeholder: '请输入库存数量',
        isRequired: true,
        isStockField: true,
        min: 0,
        max: 99999,
      },
    },
    {
      type: FieldType.DATE_PICKER,
      label: '生产日期',
      config: {
        placeholder: '请选择生产日期',
        isRequired: false,
        isStockField: false,
      },
    },
    {
      type: FieldType.SELECT,
      label: '产品分类',
      config: {
        placeholder: '请选择产品分类',
        isRequired: false,
        isStockField: false,
        options: ['手机', '电脑', '平板', '配件', '其他'],
      },
    },
  ]

  // 添加字段到表单
  demoFields.forEach((fieldData) => {
    const fieldId = formBuilderStore.addField(fieldData.type)
    if (fieldId) {
      formBuilderStore.updateFieldConfig(fieldId, fieldData.config)
      formBuilderStore.updateFieldLabel(fieldId, fieldData.label)

      // 设置库存字段
      if (fieldData.config.isStockField) {
        formBuilderStore.setStockField(fieldId)
      }
    }
  })

  // 创建演示产品数据
  const demoProducts = [
    {
      产品名称: '华为 Mate 60 Pro',
      产品规格: '12GB+512GB 昆仑青',
      库存数量: 0,
      生产日期: new Date('2024-01-15'),
      产品分类: '手机',
    },
    {
      产品名称: 'iPhone 15 Pro Max',
      产品规格: '256GB 钛原色',
      库存数量: 0,
      生产日期: new Date('2024-01-20'),
      产品分类: '手机',
    },
    {
      产品名称: '小米14 Ultra',
      产品规格: '16GB+1TB 黑色',
      库存数量: 0,
      生产日期: new Date('2024-01-25'),
      产品分类: '手机',
    },
    {
      产品名称: 'MacBook Pro M3',
      产品规格: '14英寸 18GB+512GB',
      库存数量: 0,
      生产日期: new Date('2024-01-10'),
      产品分类: '电脑',
    },
    {
      产品名称: 'iPad Pro 12.9',
      产品规格: '256GB WiFi版',
      库存数量: 0,
      生产日期: new Date('2024-01-12'),
      产品分类: '平板',
    },
  ]

  // 将字段标签映射到字段ID
  const fieldLabelToId = new Map<string, string>()
  formBuilderStore.fields.forEach((field) => {
    fieldLabelToId.set(field.label, field.id)
  })

  // 添加产品记录
  demoProducts.forEach((product) => {
    const recordData: Record<string, any> = {}

    Object.entries(product).forEach(([label, value]) => {
      const fieldId = fieldLabelToId.get(label)
      if (fieldId) {
        recordData[fieldId] = value
      }
    })

    formDataStore.addRecord(recordData)
  })

  console.log('演示数据初始化完成')
  console.log(`创建了 ${formBuilderStore.fields.length} 个字段`)
  console.log(`创建了 ${formDataStore.records.length} 个产品记录`)
}

/**
 * 执行演示库存操作
 */
export const performDemoStockOperations = () => {
  const formDataStore = useFormDataStore()

  const records = formDataStore.records
  if (records.length === 0) {
    console.warn('没有产品记录，请先初始化演示数据')
    return
  }

  // 模拟入库操作
  const stockOperations = [
    { recordIndex: 0, inbound: 100, outbound: 30 }, // 华为 Mate 60 Pro
    { recordIndex: 1, inbound: 80, outbound: 25 }, // iPhone 15 Pro Max
    { recordIndex: 2, inbound: 60, outbound: 15 }, // 小米14 Ultra
    { recordIndex: 3, inbound: 40, outbound: 10 }, // MacBook Pro M3
    { recordIndex: 4, inbound: 50, outbound: 20 }, // iPad Pro 12.9
  ]

  stockOperations.forEach(({ recordIndex, inbound, outbound }) => {
    const record = records[recordIndex]
    if (record) {
      // 入库操作
      formDataStore.operateStock({
        recordId: record.recordId,
        type: StockOperationType.INBOUND,
        quantity: inbound,
      })

      // 出库操作
      formDataStore.operateStock({
        recordId: record.recordId,
        type: StockOperationType.OUTBOUND,
        quantity: outbound,
      })
    }
  })

  console.log('演示库存操作完成')
}

/**
 * 重置所有数据
 */
export const resetAllData = () => {
  const formBuilderStore = useFormBuilderStore()
  const formDataStore = useFormDataStore()

  formBuilderStore.resetFields()
  formDataStore.resetRecords()

  console.log('所有数据已重置')
}

// 在开发环境下暴露到全局对象，方便调试
if (import.meta.env.DEV) {
  ;(window as any).demoUtils = {
    initDemoData,
    performDemoStockOperations,
    resetAllData,
  }
}
