<template>
  <div class="data-manager">
    <div class="manager-header">
      <h2>数据管理中心</h2>
      <p>管理产品记录，进行库存操作</p>
      <div class="header-actions">
        <el-button 
          type="primary" 
          @click="showAddModal = true"
          :disabled="!hasFields"
        >
          新增产品
        </el-button>
      </div>
    </div>
    
    <div class="manager-content">
      <div v-if="!hasFields" class="no-form-design">
        <el-icon :size="48" color="#ccc">
          <Warning />
        </el-icon>
        <h3>请先设计表单</h3>
        <p>您需要先在"表单设计器"中设计产品档案表单，才能开始录入数据。</p>
        <el-button type="primary" @click="$router.push('/form-designer')">
          去设计表单
        </el-button>
      </div>
      
      <div v-else-if="!hasStockField" class="no-stock-field">
        <el-icon :size="48" color="#f56c6c">
          <Warning />
        </el-icon>
        <h3>请设置库存字段</h3>
        <p>您需要在表单中设置一个数字字段作为库存字段，才能进行库存操作。</p>
        <el-button type="primary" @click="$router.push('/form-designer')">
          去设置库存字段
        </el-button>
      </div>
      
      <div v-else class="data-content">
        <DynamicDataTable />
      </div>
    </div>
    
    <!-- 数据录入/编辑弹窗 -->
    <DataFormModal 
      v-model="showAddModal"
      @success="onDataSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { useFormBuilderStore } from '@/stores'
import DynamicDataTable from '@/components/DataManager/DynamicDataTable.vue'
import DataFormModal from '@/components/DataManager/DataFormModal.vue'

const formBuilderStore = useFormBuilderStore()

const showAddModal = ref(false)

// 计算属性
const hasFields = computed(() => formBuilderStore.fields.length > 0)
const hasStockField = computed(() => formBuilderStore.hasStockField)

// 数据操作成功回调
const onDataSuccess = () => {
  showAddModal.value = false
}
</script>

<style scoped>
.data-manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.manager-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.manager-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.manager-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.manager-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.no-form-design,
.no-stock-field {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  text-align: center;
}

.no-form-design h3,
.no-stock-field h3 {
  margin: 16px 0 8px 0;
  color: #333;
}

.no-form-design p,
.no-stock-field p {
  margin: 0 0 20px 0;
  color: #666;
}

.data-content {
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}
</style>
