<template>
  <div class="log-viewer">
    <div class="viewer-header">
      <h2>出入库流水日志</h2>
      <p>查看所有库存变动的详细记录</p>
      <div class="header-actions">
        <el-button 
          v-if="logs.length > 0" 
          type="danger" 
          @click="clearAllLogs"
        >
          清空日志
        </el-button>
      </div>
    </div>
    
    <div class="viewer-content">
      <div v-if="logs.length === 0" class="no-logs">
        <el-icon :size="48" color="#ccc">
          <Document />
        </el-icon>
        <h3>暂无操作记录</h3>
        <p>当您进行库存操作时，相关记录会在这里显示</p>
      </div>
      
      <div v-else class="log-content">
        <!-- 统计信息 -->
        <div class="log-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-value">{{ logCount }}</div>
                  <div class="stat-label">总操作次数</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card inbound">
                <div class="stat-item">
                  <div class="stat-value">{{ totalInboundQuantity }}</div>
                  <div class="stat-label">总入库数量</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card outbound">
                <div class="stat-item">
                  <div class="stat-value">{{ totalOutboundQuantity }}</div>
                  <div class="stat-label">总出库数量</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-value">{{ netQuantity }}</div>
                  <div class="stat-label">净库存变化</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 日志列表 -->
        <LogList />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElButton, ElIcon, ElCard, ElRow, ElCol, ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { useLogStore } from '@/stores'
import LogList from '@/components/LogViewer/LogList.vue'

const logStore = useLogStore()

// 计算属性
const logs = computed(() => logStore.logs)
const logCount = computed(() => logStore.logCount)
const totalInboundQuantity = computed(() => logStore.totalInboundQuantity)
const totalOutboundQuantity = computed(() => logStore.totalOutboundQuantity)
const netQuantity = computed(() => totalInboundQuantity.value - totalOutboundQuantity.value)

// 清空所有日志
const clearAllLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有日志吗？此操作不可恢复。', '确认清空', {
      type: 'warning'
    })
    
    logStore.clearLogs()
    ElMessage.success('日志已清空')
  } catch {
    // 用户取消清空
  }
}
</script>

<style scoped>
.log-viewer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.viewer-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.viewer-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.viewer-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.viewer-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.no-logs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  text-align: center;
}

.no-logs h3 {
  margin: 16px 0 8px 0;
  color: #333;
}

.no-logs p {
  margin: 0;
  color: #666;
}

.log-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.log-stats {
  flex-shrink: 0;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
}

.stat-card.inbound {
  border-left: 4px solid #67c23a;
}

.stat-card.outbound {
  border-left: 4px solid #f56c6c;
}

.stat-item {
  padding: 8px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
