<template>
  <div class="main-layout">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="main-header">
        <div class="header-content">
          <div class="logo-section">
            <el-icon :size="24" color="#409eff">
              <Box />
            </el-icon>
            <h1>动态库存管理系统</h1>
          </div>
          
          <div class="header-info">
            <el-tag v-if="hasStockField" type="success" size="small">
              库存字段已设置
            </el-tag>
            <el-tag v-else type="warning" size="small">
              未设置库存字段
            </el-tag>
            <span class="record-count">
              产品记录: {{ recordCount }}
            </span>
          </div>
        </div>
      </el-header>
      
      <!-- 主要内容区域 -->
      <el-container>
        <!-- 侧边导航 -->
        <el-aside width="240px" class="main-aside">
          <el-menu
            :default-active="currentRoute"
            class="main-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item 
              v-for="route in menuRoutes" 
              :key="route.path"
              :index="route.path"
            >
              <el-icon>
                <component :is="getIcon(route.meta.icon)" />
              </el-icon>
              <span>{{ route.meta.title }}</span>
            </el-menu-item>
          </el-menu>
          
          <!-- 侧边栏底部信息 -->
          <div class="aside-footer">
            <div class="system-stats">
              <div class="stat-item">
                <span class="stat-label">字段数量:</span>
                <span class="stat-value">{{ fieldCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">操作记录:</span>
                <span class="stat-value">{{ logCount }}</span>
              </div>
            </div>
          </div>
        </el-aside>
        
        <!-- 主内容区 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ElContainer, 
  ElHeader, 
  ElAside, 
  ElMain, 
  ElMenu, 
  ElMenuItem,
  ElIcon,
  ElTag
} from 'element-plus'
import { Box, EditPen, Grid, Document } from '@element-plus/icons-vue'
import { useFormBuilderStore, useFormDataStore, useLogStore } from '@/stores'

const route = useRoute()
const router = useRouter()

const formBuilderStore = useFormBuilderStore()
const formDataStore = useFormDataStore()
const logStore = useLogStore()

// 计算属性
const currentRoute = computed(() => route.path)
const hasStockField = computed(() => formBuilderStore.hasStockField)
const fieldCount = computed(() => formBuilderStore.fields.length)
const recordCount = computed(() => formDataStore.recordCount)
const logCount = computed(() => logStore.logCount)

// 菜单路由配置
const menuRoutes = [
  {
    path: '/form-designer',
    meta: { title: '表单设计器', icon: 'EditPen' }
  },
  {
    path: '/data-manager',
    meta: { title: '数据管理中心', icon: 'Grid' }
  },
  {
    path: '/log-viewer',
    meta: { title: '出入库流水日志', icon: 'Document' }
  }
]

// 获取图标组件
const getIcon = (iconName: string) => {
  const iconMap = {
    EditPen,
    Grid,
    Document
  }
  return iconMap[iconName as keyof typeof iconMap] || Document
}

// 处理菜单选择
const handleMenuSelect = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  overflow: hidden;
}

.main-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 0;
}

.header-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-section h1 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.record-count {
  font-weight: 500;
}

.main-aside {
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.main-menu {
  border: none;
  background: transparent;
  flex: 1;
}

.main-menu .el-menu-item {
  height: 56px;
  line-height: 56px;
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.main-menu .el-menu-item:hover {
  background: #ecf5ff;
  color: #409eff;
}

.main-menu .el-menu-item.is-active {
  background: #409eff;
  color: white;
}

.aside-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  background: white;
}

.system-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.main-content {
  padding: 0;
  overflow: hidden;
}

:deep(.el-container) {
  height: 100%;
}

:deep(.el-header) {
  height: 60px !important;
}

:deep(.el-aside) {
  height: 100%;
}

:deep(.el-main) {
  height: 100%;
  padding: 0;
}
</style>
