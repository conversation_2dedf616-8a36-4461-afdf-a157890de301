<template>
  <div class="form-designer">
    <div class="designer-header">
      <h2>表单设计器</h2>
      <p>设计您的产品档案表单，定义需要记录的信息字段</p>
    </div>

    <div class="designer-content">
      <!-- 左栏：字段控件箱 -->
      <div class="left-panel">
        <FieldPalette />
      </div>

      <!-- 中栏：表单画布 -->
      <div class="center-panel">
        <FormCanvas @field-selected="onFieldSelected" />
      </div>

      <!-- 右栏：配置面板 -->
      <div class="right-panel">
        <FieldConfigPanel :selected-field-id="selectedFieldId" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FieldPalette from '@/components/FormDesigner/FieldPalette.vue'
import FormCanvas from '@/components/FormDesigner/FormCanvas.vue'
import FieldConfigPanel from '@/components/FormDesigner/FieldConfigPanel.vue'

const selectedFieldId = ref<string>('')

const onFieldSelected = (fieldId: string) => {
  selectedFieldId.value = fieldId
}
</script>

<style scoped>
.form-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.designer-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.designer-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.designer-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.designer-content {
  flex: 1;
  display: flex;
  gap: 1px;
  overflow: hidden;
}

.left-panel {
  width: 280px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  background: white;
  overflow-y: auto;
}

.right-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e0e0e0;
  overflow-y: auto;
}
</style>
